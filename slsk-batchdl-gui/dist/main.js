"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const child_process_1 = require("child_process");
const fs_1 = require("fs");
// Keep a global reference of the window object
let mainWindow = null;
let slskProcess = null;
// Store active CLI processes
const activeProcesses = new Map();
const isDev = process.env.NODE_ENV === 'development';
function createWindow() {
    // Create the browser window
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: (0, path_1.join)(__dirname, 'preload.js'),
        },
        icon: (0, path_1.join)(__dirname, '../../assets/icon.png'), // We'll add this later
        show: false, // Don't show until ready
    });
    // Load the app
    if (isDev) {
        // Try different ports in case 3000 is occupied
        const devUrl = process.env.VITE_DEV_SERVER_URL || 'http://localhost:3000';
        mainWindow.loadURL(devUrl);
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile((0, path_1.join)(__dirname, '../renderer/index.html'));
    }
    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
    });
    // Handle window closed
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        electron_1.shell.openExternal(url);
        return { action: 'deny' };
    });
}
// App event handlers
electron_1.app.whenReady().then(() => {
    createWindow();
    electron_1.app.on('activate', () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});
electron_1.app.on('window-all-closed', () => {
    // Kill any running slsk process
    if (slskProcess) {
        slskProcess.kill();
    }
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
electron_1.app.on('before-quit', () => {
    // Clean up any running processes
    if (slskProcess) {
        slskProcess.kill();
    }
});
// IPC handlers
electron_1.ipcMain.handle('select-directory', async () => {
    const result = await electron_1.dialog.showOpenDialog(mainWindow, {
        properties: ['openDirectory'],
    });
    return result.canceled ? null : result.filePaths[0];
});
electron_1.ipcMain.handle('select-file', async (_, filters) => {
    const result = await electron_1.dialog.showOpenDialog(mainWindow, {
        properties: ['openFile'],
        filters: filters || [{ name: 'All Files', extensions: ['*'] }],
    });
    return result.canceled ? null : result.filePaths[0];
});
electron_1.ipcMain.handle('save-file', async (_, defaultPath, filters) => {
    const result = await electron_1.dialog.showSaveDialog(mainWindow, {
        defaultPath,
        filters: filters || [{ name: 'All Files', extensions: ['*'] }],
    });
    return result.canceled ? null : result.filePath;
});
electron_1.ipcMain.handle('read-file', async (_, filePath) => {
    try {
        if (!(0, fs_1.existsSync)(filePath)) {
            return null;
        }
        return (0, fs_1.readFileSync)(filePath, 'utf-8');
    }
    catch (error) {
        console.error('Error reading file:', error);
        return null;
    }
});
electron_1.ipcMain.handle('write-file', async (_, filePath, content) => {
    try {
        (0, fs_1.writeFileSync)(filePath, content, 'utf-8');
        return true;
    }
    catch (error) {
        console.error('Error writing file:', error);
        return false;
    }
});
electron_1.ipcMain.handle('file-exists', async (_, filePath) => {
    return (0, fs_1.existsSync)(filePath);
});
// SLSK process management
electron_1.ipcMain.handle('start-slsk-process', async (_, args) => {
    return new Promise((resolve, reject) => {
        try {
            // Kill existing process if running
            if (slskProcess) {
                slskProcess.kill();
            }
            // Find the slsk-batchdl executable
            // This assumes it's in the same directory or a known location
            const slskPath = (0, path_1.join)(__dirname, '../../../slsk-batchdl/slsk-batchdl'); // Adjust path as needed
            slskProcess = (0, child_process_1.spawn)(slskPath, args, {
                stdio: ['pipe', 'pipe', 'pipe'],
            });
            let stdout = '';
            let stderr = '';
            slskProcess.stdout?.on('data', (data) => {
                stdout += data.toString();
                // Send real-time output to renderer
                mainWindow?.webContents.send('slsk-output', {
                    type: 'stdout',
                    data: data.toString(),
                });
            });
            slskProcess.stderr?.on('data', (data) => {
                stderr += data.toString();
                // Send real-time output to renderer
                mainWindow?.webContents.send('slsk-output', {
                    type: 'stderr',
                    data: data.toString(),
                });
            });
            slskProcess.on('close', (code) => {
                mainWindow?.webContents.send('slsk-process-exit', {
                    code,
                    stdout,
                    stderr,
                });
                slskProcess = null;
            });
            slskProcess.on('error', (error) => {
                reject(error);
                slskProcess = null;
            });
            resolve({ pid: slskProcess.pid });
        }
        catch (error) {
            reject(error);
        }
    });
});
electron_1.ipcMain.handle('stop-slsk-process', async () => {
    if (slskProcess) {
        slskProcess.kill();
        slskProcess = null;
        return true;
    }
    return false;
});
electron_1.ipcMain.handle('send-to-slsk-process', async (_, input) => {
    if (slskProcess && slskProcess.stdin) {
        slskProcess.stdin.write(input + '\n');
        return true;
    }
    return false;
});
// New CLI process management for multiple processes
electron_1.ipcMain.handle('start-cli-process', async (_, args) => {
    return new Promise((resolve, reject) => {
        try {
            // TODO: Replace with actual path to slsk-batchdl executable
            const executablePath = isDev
                ? 'dotnet' // For development, use dotnet to run the project
                : (0, path_1.join)(process.resourcesPath, 'slsk-batchdl.exe'); // For production
            const processArgs = isDev
                ? ['run', '--project', '../slsk-batchdl', '--', ...args] // Development args
                : args; // Production args
            const childProcess = (0, child_process_1.spawn)(executablePath, processArgs, {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: isDev ? (0, path_1.join)(__dirname, '../../..') : undefined,
            });
            const pid = childProcess.pid;
            activeProcesses.set(pid, childProcess);
            // Set up event listeners
            childProcess.stdout?.on('data', (data) => {
                mainWindow?.webContents.send('cli-output', pid, data.toString());
            });
            childProcess.stderr?.on('data', (data) => {
                mainWindow?.webContents.send('cli-error', pid, data.toString());
            });
            childProcess.on('exit', (code) => {
                activeProcesses.delete(pid);
                mainWindow?.webContents.send('cli-exit', pid, code);
            });
            childProcess.on('error', (error) => {
                activeProcesses.delete(pid);
                mainWindow?.webContents.send('cli-error', pid, error.message);
            });
            resolve({ pid });
        }
        catch (error) {
            reject(error);
        }
    });
});
electron_1.ipcMain.handle('stop-cli-process', async (_, pid) => {
    const process = activeProcesses.get(pid);
    if (process) {
        process.kill();
        activeProcesses.delete(pid);
        return true;
    }
    return false;
});
// Get app version
electron_1.ipcMain.handle('get-app-version', () => {
    return electron_1.app.getVersion();
});
// Show message box
electron_1.ipcMain.handle('show-message-box', async (_, options) => {
    const result = await electron_1.dialog.showMessageBox(mainWindow, options);
    return result;
});
