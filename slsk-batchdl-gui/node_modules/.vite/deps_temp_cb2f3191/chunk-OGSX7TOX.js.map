{"version": 3, "sources": ["../../@mui/material/DefaultPropsProvider/DefaultPropsProvider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport SystemDefaultPropsProvider, { useDefaultProps as useSystemDefaultProps } from '@mui/system/DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction DefaultPropsProvider(props) {\n  return /*#__PURE__*/_jsx(SystemDefaultPropsProvider, _extends({}, props));\n}\nprocess.env.NODE_ENV !== \"production\" ? DefaultPropsProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  value: PropTypes.object.isRequired\n} : void 0;\nexport default DefaultPropsProvider;\nexport function useDefaultProps(params) {\n  return useSystemDefaultProps(params);\n}"], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA,YAAuB;AACvB,wBAAsB;AAEtB,yBAA4B;AAC5B,SAAS,qBAAqB,OAAO;AACnC,aAAoB,mBAAAA,KAAK,8BAA4B,SAAS,CAAC,GAAG,KAAK,CAAC;AAC1E;AACA,OAAwC,qBAAqB,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9F,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,OAAO,kBAAAA,QAAU,OAAO;AAC1B,IAAI;AAEG,SAASC,iBAAgB,QAAQ;AACtC,SAAO,gBAAsB,MAAM;AACrC;", "names": ["_jsx", "PropTypes", "useDefaultProps"]}