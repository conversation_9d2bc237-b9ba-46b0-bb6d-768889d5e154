declare const allPresets: {
    readonly defaults: import("..").DefaultPreset;
    readonly defaultsLegacy: import("..").DefaultLegacyPreset;
    readonly defaultsESM: import("..").DefaultEsmPreset;
    readonly defaultsESMLegacy: import("..").DefaultEsmLegacyPreset;
    readonly jsWithTs: import("..").JsWithTsPreset;
    readonly jsWithTsLegacy: import("..").JsWithTsLegacyPreset;
    readonly jsWithTsESM: import("..").JsWithTsEsmPreset;
    readonly jsWithTsESMLegacy: import("..").JsWithTsEsmLegacyPreset;
    readonly jsWithBabel: import("..").JsWithBabelPreset;
    readonly jsWithBabelLegacy: import("..").JsWithBabelLegacyPreset;
    readonly jsWithBabelESM: import("..").JsWithBabelEsmPreset;
    readonly jsWithBabelESMLegacy: import("..").JsWithBabelEsmLegacyPreset;
};
export default allPresets;
