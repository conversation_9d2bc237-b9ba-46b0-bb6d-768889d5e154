{"name": "jest-extended", "version": "4.0.2", "description": "Additional Jest matchers", "main": "dist/index.js", "types": "types/index.d.ts", "files": ["dist", "types/index.d.ts", "all.js"], "scripts": {"build": "babel src -d dist --ignore \"**/*.test.js\"", "lint": "eslint .", "lint:fix": "yarn lint --fix", "prepare": "husky install", "prepublishOnly": "yarn build", "precommit": "lint-staged", "test": "jest --color=true", "test:coverage": "yarn test --coverage", "test:watch": "yarn test --watch", "typecheck": "tsc --noEmit types/index.d.ts", "dev:docs": "cd website && yarn start"}, "keywords": ["jest", "matchers", "extend", "extended", "test", "testing", "assertions"], "author": "<PERSON> <<EMAIL>> (mattphillips.io)", "license": "MIT", "repository": "jest-community/jest-extended", "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.14.7", "@changesets/cli": "^2.26.0", "@jest/expect-utils": "^29.0.0", "@types/jest": "^29.0.0", "@typescript-eslint/eslint-plugin": "^5.30.6", "@typescript-eslint/parser": "^5.30.6", "babel-jest": "^29.0.0", "babel-jest-assertions": "^0.1.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.2.5", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jest": "^27.0.0", "eslint-plugin-prettier": "^4.0.0", "husky": "^8.0.0", "jest": "^29.0.0", "jest-serializer-ansi-escapes": "^2.0.1", "jest-watch-typeahead": "^2.0.0", "lint-staged": "~13.2.0", "prettier": "^2.3.2", "typescript": "^5.0.0"}, "dependencies": {"jest-diff": "^29.0.0", "jest-get-type": "^29.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "lint-staged": {"*.js": "eslint --fix", "*.md": "prettier --write"}, "jest": {"testEnvironment": "node", "testPathIgnorePatterns": [".idea", "examples", "/node_modules/", "/fixtures/", "/dist/"], "coveragePathIgnorePatterns": ["/node_modules/"], "snapshotFormat": {"escapeString": false, "printBasicPrototype": false}, "snapshotSerializers": ["jest-serializer-ansi-escapes"], "moduleNameMapper": {"src/(.*)": "<rootDir>/src/$1"}, "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 100}}, "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "14"}}]], "plugins": ["module:babel-jest-assertions"]}, "prettier": {"arrowParens": "avoid", "printWidth": 120, "singleQuote": true, "trailingComma": "all"}, "peerDependencies": {"jest": ">=27.2.5"}, "peerDependenciesMeta": {"jest": {"optional": true}}}