"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.toIncludeAllPartialMembers = toIncludeAllPartialMembers;
var _utils = require("../utils");
function toIncludeAllPartialMembers(actual, expected) {
  const {
    printReceived,
    printExpected,
    matcherHint
  } = this.utils;
  const pass = Array.isArray(actual) && Array.isArray(expected) && expected.every(partial => actual.some(value => Object.entries(partial).every(entry => (0, _utils.containsEntry)(this.equals, value, entry))));
  return {
    pass,
    message: () => pass ? matcherHint('.not.toIncludeAllPartialMembers') + '\n\n' + 'Expected list to not have all of the following partial members:\n' + `  ${printExpected(expected)}\n` + 'Received:\n' + `  ${printReceived(actual)}` : matcherHint('.toIncludeAllPartialMembers') + '\n\n' + 'Expected list to have all of the following partial members:\n' + `  ${printExpected(expected)}\n` + 'Received:\n' + `  ${printReceived(actual)}`
  };
}