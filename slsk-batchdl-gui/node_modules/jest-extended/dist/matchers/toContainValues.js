"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.toContainValues = toContainValues;
var _utils = require("../utils");
function toContainValues(actual, expected) {
  const {
    printReceived,
    printExpected,
    matcherHint
  } = this.utils;
  const values = Object.keys(actual).map(k => actual[k]);
  const pass = expected.every(value => (0, _utils.contains)(this.equals, values, value));
  return {
    pass,
    message: () => pass ? matcherHint('.not.toContainValues') + '\n\n' + 'Expected object to not contain all values:\n' + `  ${printExpected(expected)}\n` + 'Received:\n' + `  ${printReceived(actual)}` : matcherHint('.toContainValues') + '\n\n' + 'Expected object to contain all values:\n' + `  ${printExpected(expected)}\n` + 'Received:\n' + `  ${printReceived(actual)}`
  };
}