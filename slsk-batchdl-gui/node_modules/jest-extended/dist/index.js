"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _matchers = require("./matchers");
Object.keys(_matchers).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _matchers[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _matchers[key];
    }
  });
});