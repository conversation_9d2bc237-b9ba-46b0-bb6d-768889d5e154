{"name": "slsk-batchdl-gui", "version": "1.0.0", "description": "A modern GUI for slsk-batchdl - Soulseek batch downloader", "main": "dist/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:main": "sleep 3 && tsc -p tsconfig.main.json && cross-env NODE_ENV=development electron dist/main.js", "dev:renderer": "vite", "build": "npm run build:main && npm run build:renderer", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "vite build", "package": "npm run build && electron-builder", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=__tests__ --testPathIgnorePatterns=integration", "optimize": "node scripts/optimize-build.js", "build:optimized": "npm run build && npm run optimize"}, "keywords": ["soulseek", "music", "downloader", "electron", "gui", "batch-download"], "author": "slsk-batchdl-gui contributors", "license": "MIT", "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "babel-jest": "^29.6.0", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "electron": "^25.0.0", "electron-builder": "^24.0.0", "eslint": "^8.45.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.6.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "jest-watch-typeahead": "^2.2.2", "ts-jest": "^29.1.0", "typescript": "^5.1.0", "vite": "^4.4.0", "wait-on": "^7.0.1"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.0", "@mui/material": "^5.14.0", "@mui/x-data-grid": "^6.10.0", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.0", "react-query": "^3.39.0", "react-router-dom": "^6.14.0", "zustand": "^4.4.0"}, "build": {"appId": "com.slsk-batchdl.gui", "productName": "SLSK BatchDL GUI", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "mac": {"category": "public.app-category.music"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}