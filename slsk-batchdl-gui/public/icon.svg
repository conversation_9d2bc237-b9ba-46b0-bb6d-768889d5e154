<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="#1976d2" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Music note icon -->
  <g transform="translate(16, 12)">
    <!-- Main note stem -->
    <rect x="24" y="8" width="3" height="28" fill="white"/>
    
    <!-- Note head -->
    <ellipse cx="21" cy="36" rx="4" ry="3" fill="white"/>
    
    <!-- Eighth note flag -->
    <path d="M27 8 C35 6, 35 14, 27 16 Z" fill="white"/>
    
    <!-- Second note (for harmony) -->
    <rect x="12" y="16" width="3" height="20" fill="white"/>
    <ellipse cx="9" cy="36" rx="4" ry="3" fill="white"/>
    
    <!-- Download arrow -->
    <g transform="translate(4, 20)">
      <path d="M4 8 L8 12 L12 8" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
      <line x1="8" y1="4" x2="8" y2="12" stroke="white" stroke-width="2" stroke-linecap="round"/>
    </g>
  </g>
  
  <!-- App name text -->
  <text x="32" y="52" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="8" font-weight="bold">SLSK</text>
</svg>
