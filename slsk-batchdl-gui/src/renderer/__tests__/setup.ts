import '@testing-library/jest-dom'
import 'jest-extended'

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() { }
  disconnect() { }
  observe() { }
  unobserve() { }
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() { }
  disconnect() { }
  observe() { }
  unobserve() { }
}

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.sessionStorage = sessionStorageMock

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url')
global.URL.revokeObjectURL = jest.fn()

// Mock Electron API
const mockElectronAPI = {
  selectDirectory: jest.fn().mockResolvedValue('/mock/directory'),
  selectFile: jest.fn().mockResolvedValue('/mock/file.txt'),
  saveFile: jest.fn().mockResolvedValue(undefined),
  readFile: jest.fn().mockResolvedValue('mock file content'),
  writeFile: jest.fn().mockResolvedValue(undefined),
  fileExists: jest.fn().mockResolvedValue(true),
  startSlskProcess: jest.fn().mockResolvedValue({ pid: 12345 }),
  stopSlskProcess: jest.fn().mockResolvedValue(undefined),
  sendToSlskProcess: jest.fn().mockResolvedValue(undefined),
  startCliProcess: jest.fn().mockResolvedValue({ pid: 12345 }),
  stopCliProcess: jest.fn().mockResolvedValue(undefined),
  onCliOutput: jest.fn(),
  onCliError: jest.fn(),
  onCliExit: jest.fn(),
  onSlskOutput: jest.fn(),
  onSlskProcessExit: jest.fn(),
  getAppVersion: jest.fn().mockReturnValue('1.0.0'),
  showMessageBox: jest.fn().mockResolvedValue({ response: 0 }),
}

// @ts-ignore
global.window.electronAPI = mockElectronAPI

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to ignore specific console methods
  // log: jest.fn(),
  // debug: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
}

// Setup for async testing
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks()

  // Reset localStorage mock
  localStorageMock.getItem.mockReturnValue(null)
  localStorageMock.setItem.mockReturnValue(undefined)
  localStorageMock.removeItem.mockReturnValue(undefined)
  localStorageMock.clear.mockReturnValue(undefined)

  // Reset sessionStorage mock
  sessionStorageMock.getItem.mockReturnValue(null)
  sessionStorageMock.setItem.mockReturnValue(undefined)
  sessionStorageMock.removeItem.mockReturnValue(undefined)
  sessionStorageMock.clear.mockReturnValue(undefined)
})

// Global test timeout
jest.setTimeout(10000)

// Suppress specific warnings
const originalError = console.error
const originalWarn = console.warn
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is deprecated') ||
        args[0].includes('Warning: `ReactDOMTestUtils.act` is deprecated') ||
        args[0].includes('Warning: Maximum update depth exceeded'))
    ) {
      return
    }
    originalError.call(console, ...args)
  }

  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('React Router Future Flag Warning') ||
        args[0].includes('MUI: You have provided an out-of-range value'))
    ) {
      return
    }
    originalWarn.call(console, ...args)
  }
})

afterAll(() => {
  console.error = originalError
  console.warn = originalWarn
})
