import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import FileManager from '../../components/FileManager'

const theme = createTheme()

// Mock the NotificationSystem
jest.mock('../../components/NotificationSystem', () => ({
  useNotifications: () => ({
    showSuccess: jest.fn(),
    showError: jest.fn(),
    showInfo: jest.fn(),
  })
}))

// Mock the electron API
const mockElectronAPI = {
  selectDirectory: jest.fn(),
}

// @ts-ignore
global.window = {
  electronAPI: mockElectronAPI
}

const defaultProps = {
  outputPath: '/music/downloads',
  onOutputPathChange: jest.fn(),
  namingTemplate: '{artist} - {album} - {track:02d} - {title}',
  onNamingTemplateChange: jest.fn(),
  createSubfolders: true,
  onCreateSubfoldersChange: jest.fn(),
  postDownloadActions: [],
  onPostDownloadActionsChange: jest.fn(),
}

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  )
}

describe('FileManager', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Output Directory', () => {
    it('renders output directory section', () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      expect(screen.getByText('Output Directory')).toBeInTheDocument()
      expect(screen.getByDisplayValue('/music/downloads')).toBeInTheDocument()
      expect(screen.getByText('Browse')).toBeInTheDocument()
    })

    it('shows warning when no output path is set', () => {
      renderWithTheme(<FileManager {...defaultProps} outputPath="" />)

      expect(screen.getByText(/No output directory selected/)).toBeInTheDocument()
    })

    it('calls onOutputPathChange when path is manually entered', () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      const input = screen.getByDisplayValue('/music/downloads')
      fireEvent.change(input, { target: { value: '/new/path' } })

      expect(defaultProps.onOutputPathChange).toHaveBeenCalledWith('/new/path')
    })

    it('opens directory browser when browse button is clicked', async () => {
      mockElectronAPI.selectDirectory.mockResolvedValue('/selected/path')

      renderWithTheme(<FileManager {...defaultProps} />)

      const browseButton = screen.getByText('Browse')
      fireEvent.click(browseButton)

      await waitFor(() => {
        expect(mockElectronAPI.selectDirectory).toHaveBeenCalled()
        expect(defaultProps.onOutputPathChange).toHaveBeenCalledWith('/selected/path')
      })
    })

    it('handles directory selection cancellation', async () => {
      mockElectronAPI.selectDirectory.mockResolvedValue(null)

      renderWithTheme(<FileManager {...defaultProps} />)

      const browseButton = screen.getByText('Browse')
      fireEvent.click(browseButton)

      await waitFor(() => {
        expect(mockElectronAPI.selectDirectory).toHaveBeenCalled()
        expect(defaultProps.onOutputPathChange).not.toHaveBeenCalled()
      })
    })
  })

  describe('File Naming Template', () => {
    it('renders naming template section', () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      expect(screen.getByText('File Naming Template')).toBeInTheDocument()
      expect(screen.getByDisplayValue('{artist} - {album} - {track:02d} - {title}')).toBeInTheDocument()
      expect(screen.getByText('Templates')).toBeInTheDocument()
    })

    it('shows template preview', () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      expect(screen.getByText(/Preview:/)).toBeInTheDocument()
      expect(screen.getByText(/The Beatles - Abbey Road - 01 - Come Together.mp3/)).toBeInTheDocument()
    })

    it('calls onNamingTemplateChange when template is modified', () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      const input = screen.getByDisplayValue('{artist} - {album} - {track:02d} - {title}')
      fireEvent.change(input, { target: { value: '{artist} - {title}' } })

      expect(defaultProps.onNamingTemplateChange).toHaveBeenCalledWith('{artist} - {title}')
    })

    it('opens template dialog when Templates button is clicked', async () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      const templatesButton = screen.getByText('Templates')
      fireEvent.click(templatesButton)

      await waitFor(() => {
        expect(screen.getByText('File Naming Templates')).toBeInTheDocument()
        expect(screen.getByText('Predefined Templates')).toBeInTheDocument()
        expect(screen.getByText('Custom Template')).toBeInTheDocument()
      })
    })

    it('applies predefined template when selected', async () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      const templatesButton = screen.getByText('Templates')
      fireEvent.click(templatesButton)

      await waitFor(() => {
        const artistTitleTemplate = screen.getByText('Artist - Title')
        fireEvent.click(artistTitleTemplate)
      })

      expect(defaultProps.onNamingTemplateChange).toHaveBeenCalledWith('{artist} - {title}')
    })

    it('handles subfolder toggle', () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      const toggle = screen.getByRole('checkbox', { name: /Create subfolders/ })
      fireEvent.click(toggle)

      expect(defaultProps.onCreateSubfoldersChange).toHaveBeenCalledWith(false)
    })
  })

  describe('Post-Download Actions', () => {
    it('renders post-download actions section', () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      expect(screen.getByText('Post-Download Actions')).toBeInTheDocument()
      expect(screen.getByText('Add Action')).toBeInTheDocument()
    })

    it('shows message when no actions are configured', () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      expect(screen.getByText(/No post-download actions configured/)).toBeInTheDocument()
    })

    it('displays existing actions', () => {
      const actionsProps = {
        ...defaultProps,
        postDownloadActions: [
          {
            id: '1',
            name: 'Create Playlist',
            type: 'playlist' as const,
            enabled: true,
            config: {}
          }
        ]
      }

      renderWithTheme(<FileManager {...actionsProps} />)

      expect(screen.getByText('Create Playlist')).toBeInTheDocument()
      expect(screen.getByText('playlist')).toBeInTheDocument()
    })

    it('opens action dialog when Add Action is clicked', async () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      const addButton = screen.getByText('Add Action')
      fireEvent.click(addButton)

      await waitFor(() => {
        expect(screen.getByText('Add Action')).toBeInTheDocument()
        expect(screen.getByLabelText('Action Name')).toBeInTheDocument()
        expect(screen.getByLabelText('Action Type')).toBeInTheDocument()
      })
    })

    it('validates action name when saving', async () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      const addButton = screen.getByText('Add Action')
      fireEvent.click(addButton)

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save' })
        fireEvent.click(saveButton)
      })

      // Should not call the change handler without a name
      expect(defaultProps.onPostDownloadActionsChange).not.toHaveBeenCalled()
    })

    it('creates new action with valid data', async () => {
      renderWithTheme(<FileManager {...defaultProps} />)

      const addButton = screen.getByText('Add Action')
      fireEvent.click(addButton)

      await waitFor(async () => {
        const nameInput = screen.getByLabelText('Action Name')
        fireEvent.change(nameInput, { target: { value: 'Test Action' } })

        const saveButton = screen.getByRole('button', { name: 'Save' })
        fireEvent.click(saveButton)
      })

      expect(defaultProps.onPostDownloadActionsChange).toHaveBeenCalledWith([
        expect.objectContaining({
          name: 'Test Action',
          type: 'playlist',
          enabled: true
        })
      ])
    })

    it('toggles action enabled state', () => {
      const actionsProps = {
        ...defaultProps,
        postDownloadActions: [
          {
            id: '1',
            name: 'Create Playlist',
            type: 'playlist' as const,
            enabled: true,
            config: {}
          }
        ]
      }

      renderWithTheme(<FileManager {...actionsProps} />)

      const toggles = screen.getAllByRole('checkbox')
      const toggle = toggles[1] // Second checkbox is for the action
      fireEvent.click(toggle)

      expect(defaultProps.onPostDownloadActionsChange).toHaveBeenCalledWith([
        expect.objectContaining({
          id: '1',
          enabled: false
        })
      ])
    })

    it('deletes action when delete button is clicked', () => {
      const actionsProps = {
        ...defaultProps,
        postDownloadActions: [
          {
            id: '1',
            name: 'Create Playlist',
            type: 'playlist' as const,
            enabled: true,
            config: {}
          }
        ]
      }

      renderWithTheme(<FileManager {...actionsProps} />)

      const deleteButton = screen.getByRole('button', { name: '' }) // Delete icon button
      fireEvent.click(deleteButton)

      expect(defaultProps.onPostDownloadActionsChange).toHaveBeenCalledWith([])
    })
  })

  describe('Template Preview', () => {
    it('updates preview when template changes', () => {
      const { rerender } = renderWithTheme(<FileManager {...defaultProps} />)

      expect(screen.getByText(/The Beatles - Abbey Road - 01 - Come Together.mp3/)).toBeInTheDocument()

      rerender(
        <ThemeProvider theme={theme}>
          <FileManager {...defaultProps} namingTemplate="{artist} - {title}" />
        </ThemeProvider>
      )

      expect(screen.getByText(/The Beatles - Come Together.mp3/)).toBeInTheDocument()
    })

    it('handles zero-padded track numbers', () => {
      renderWithTheme(<FileManager {...defaultProps} namingTemplate="{track:03d} - {title}" />)

      expect(screen.getByText(/001 - Come Together.mp3/)).toBeInTheDocument()
    })

    it('handles templates with folder separators', () => {
      renderWithTheme(<FileManager {...defaultProps} namingTemplate="{artist}/{album}/{title}" />)

      expect(screen.getByText(/The Beatles\/Abbey Road\/Come Together.mp3/)).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('handles directory selection errors gracefully', async () => {
      mockElectronAPI.selectDirectory.mockRejectedValue(new Error('Permission denied'))

      renderWithTheme(<FileManager {...defaultProps} />)

      const browseButton = screen.getByText('Browse')
      fireEvent.click(browseButton)

      await waitFor(() => {
        expect(mockElectronAPI.selectDirectory).toHaveBeenCalled()
        // Should not crash or call onOutputPathChange
        expect(defaultProps.onOutputPathChange).not.toHaveBeenCalled()
      })
    })
  })
})
