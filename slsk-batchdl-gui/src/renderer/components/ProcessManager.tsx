import React, { useState, useEffect, useCallback } from 'react'
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
} from '@mui/material'
import {
  PlayArrow as StartIcon,
  Stop as StopIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  Computer as ProcessIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Pause as PauseIcon,
} from '@mui/icons-material'

import { cliService, ProcessInfo, DownloadProgress, CliOutput } from '../services/CliService'
import { useNotifications } from './NotificationSystem'

interface ProcessManagerProps {
  onProcessStart?: (processId: string) => void
  onProcessStop?: (processId: string) => void
}

const ProcessManager: React.FC<ProcessManagerProps> = ({
  onProcessStart,
  onProcessStop,
}) => {
  const [processes, setProcesses] = useState<Map<string, ProcessInfo>>(new Map())
  const [downloads, setDownloads] = useState<Map<string, DownloadProgress>>(new Map())
  const [outputs, setOutputs] = useState<Map<string, CliOutput[]>>(new Map())
  const [selectedProcess, setSelectedProcess] = useState<string | null>(null)
  const [outputDialogOpen, setOutputDialogOpen] = useState(false)

  const { showSuccess, showError, showInfo } = useNotifications()

  // Memoize event handlers to prevent unnecessary re-renders
  const handleProcessStarted = useCallback(({ processId, processInfo }: any) => {
    setProcesses(prev => new Map(prev.set(processId, processInfo)))
    showSuccess(`Process started: ${processInfo.config.input}`)
    onProcessStart?.(processId)
  }, [showSuccess, onProcessStart])

  const handleProcessStopped = useCallback(({ processId }: any) => {
    setProcesses(prev => {
      const newMap = new Map(prev)
      const process = newMap.get(processId)
      if (process) {
        process.status = 'stopped'
        newMap.set(processId, process)
      }
      return newMap
    })
    showInfo('Process stopped')
    onProcessStop?.(processId)
  }, [showInfo, onProcessStop])

  const handleProcessExited = useCallback(({ processId, exitCode }: any) => {
    setProcesses(prev => {
      const newMap = new Map(prev)
      const process = newMap.get(processId)
      if (process) {
        process.status = exitCode === 0 ? 'stopped' : 'error'
        newMap.set(processId, process)
      }
      return newMap
    })

    if (exitCode === 0) {
      showSuccess('Process completed successfully')
    } else {
      showError(`Process exited with error code: ${exitCode}`)
    }
  }, [showSuccess, showError])

  const handleOutput = useCallback(({ processId, output }: any) => {
    setOutputs(prev => {
      const newMap = new Map(prev)
      const processOutputs = newMap.get(processId) || []
      processOutputs.push(output)
      // Keep only last 1000 lines
      if (processOutputs.length > 1000) {
        processOutputs.splice(0, processOutputs.length - 1000)
      }
      newMap.set(processId, processOutputs)
      return newMap
    })
  }, [])

  const handleDownloadProgress = useCallback((progress: DownloadProgress) => {
    setDownloads(prev => new Map(prev.set(progress.id, progress)))
  }, [])

  const handleError = useCallback(({ processId, error }: any) => {
    showError(`Process error: ${error.message}`)
  }, [showError])

  useEffect(() => {
    // Set up CLI service event listeners

    // Add event listeners
    cliService.on('processStarted', handleProcessStarted)
    cliService.on('processStopped', handleProcessStopped)
    cliService.on('processExited', handleProcessExited)
    cliService.on('output', handleOutput)
    cliService.on('downloadProgress', handleDownloadProgress)
    cliService.on('error', handleError)

    // Load existing processes only once on mount
    const initialProcesses = cliService.getProcesses()
    const initialDownloads = cliService.getDownloads()

    setProcesses(initialProcesses)
    setDownloads(initialDownloads)

    return () => {
      // Remove event listeners
      cliService.off('processStarted', handleProcessStarted)
      cliService.off('processStopped', handleProcessStopped)
      cliService.off('processExited', handleProcessExited)
      cliService.off('output', handleOutput)
      cliService.off('downloadProgress', handleDownloadProgress)
      cliService.off('error', handleError)
    }
  }, [handleProcessStarted, handleProcessStopped, handleProcessExited, handleOutput, handleDownloadProgress, handleError])

  const handleStopProcess = async (processId: string) => {
    try {
      await cliService.stopProcess(processId)
    } catch (error) {
      showError(`Failed to stop process: ${error}`)
    }
  }

  const handleViewOutput = (processId: string) => {
    setSelectedProcess(processId)
    setOutputDialogOpen(true)
  }

  const handleDeleteProcess = (processId: string) => {
    const processInfo = processes.get(processId)
    if (processInfo?.status === 'running') {
      showError('Cannot delete running process. Stop it first.')
      return
    }

    setProcesses(prev => {
      const newMap = new Map(prev)
      newMap.delete(processId)
      return newMap
    })

    setOutputs(prev => {
      const newMap = new Map(prev)
      newMap.delete(processId)
      return newMap
    })

    showSuccess('Process removed')
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'primary'
      case 'stopped':
        return 'default'
      case 'error':
        return 'error'
      default:
        return 'default'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <ProcessIcon color="primary" />
      case 'stopped':
        return <SuccessIcon color="action" />
      case 'error':
        return <ErrorIcon color="error" />
      default:
        return <ProcessIcon />
    }
  }

  const processArray = Array.from(processes.entries())
  const downloadArray = Array.from(downloads.values())
  const activeDownloads = downloadArray.filter(d => d.status === 'downloading')

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Process Manager
      </Typography>

      {/* Active Downloads Summary */}
      {activeDownloads.length > 0 && (
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>
              Active Downloads ({activeDownloads.length})
            </Typography>
            {activeDownloads.slice(0, 3).map((download) => (
              <Box key={download.id} sx={{ mb: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                  <Typography variant="body2" noWrap sx={{ maxWidth: '60%' }}>
                    {download.filename}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {download.progress}% • {download.speed} • {download.eta}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={download.progress}
                  sx={{ height: 4, borderRadius: 2 }}
                />
              </Box>
            ))}
            {activeDownloads.length > 3 && (
              <Typography variant="caption" color="text.secondary">
                +{activeDownloads.length - 3} more downloads...
              </Typography>
            )}
          </CardContent>
        </Card>
      )}

      {/* Process List */}
      {processArray.length === 0 ? (
        <Alert severity="info">
          No active processes. Start a download to see processes here.
        </Alert>
      ) : (
        <Card>
          <CardContent>
            <List>
              {processArray.map(([processId, processInfo], index) => (
                <React.Fragment key={processId}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getStatusIcon(processInfo.status)}
                          <Typography variant="subtitle2">
                            {processInfo.config.input}
                          </Typography>
                          <Chip
                            label={processInfo.status}
                            size="small"
                            color={getStatusColor(processInfo.status)}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            PID: {processInfo.pid} • Started: {processInfo.startTime.toLocaleTimeString()}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Mode: {processInfo.config.searchMode} •
                            Quality: {processInfo.config.minBitrate}-{processInfo.config.maxBitrate}kbps
                          </Typography>
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewOutput(processId)}
                          title="View Output"
                        >
                          <ViewIcon />
                        </IconButton>
                        {processInfo.status === 'running' ? (
                          <IconButton
                            size="small"
                            onClick={() => handleStopProcess(processId)}
                            color="error"
                            title="Stop Process"
                          >
                            <StopIcon />
                          </IconButton>
                        ) : (
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteProcess(processId)}
                            color="error"
                            title="Remove Process"
                          >
                            <DeleteIcon />
                          </IconButton>
                        )}
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < processArray.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Output Dialog */}
      <Dialog
        open={outputDialogOpen}
        onClose={() => setOutputDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Process Output
          {selectedProcess && processes.get(selectedProcess) && (
            <Typography variant="subtitle2" color="text.secondary">
              {processes.get(selectedProcess)?.config.input}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent>
          <Box
            sx={{
              bgcolor: 'grey.900',
              color: 'grey.100',
              p: 2,
              borderRadius: 1,
              fontFamily: 'monospace',
              fontSize: '0.875rem',
              maxHeight: 400,
              overflow: 'auto',
            }}
          >
            {selectedProcess && outputs.get(selectedProcess)?.map((output, index) => (
              <Box key={index} sx={{ mb: 0.5 }}>
                <Typography
                  component="span"
                  sx={{
                    color: output.type === 'error' ? 'error.main' :
                      output.type === 'warning' ? 'warning.main' :
                        output.type === 'success' ? 'success.main' :
                          output.type === 'progress' ? 'info.main' :
                            'inherit'
                  }}
                >
                  [{output.timestamp.toLocaleTimeString()}] {output.message}
                </Typography>
              </Box>
            )) || (
                <Typography color="text.secondary">No output available</Typography>
              )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOutputDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ProcessManager
