import { app, BrowserWindow, ipc<PERSON>ain, dialog, shell } from 'electron'
import { join } from 'path'
import { spawn, ChildProcess } from 'child_process'
import { existsSync, readFileSync, writeFileSync } from 'fs'

// Keep a global reference of the window object
let mainWindow: BrowserWindow | null = null
let slskProcess: ChildProcess | null = null

// Store active CLI processes
const activeProcesses = new Map<number, ChildProcess>()

const isDev = process.env.NODE_ENV === 'development'

function createWindow(): void {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: join(__dirname, 'preload.js'),
    },
    icon: join(__dirname, '../../assets/icon.png'), // We'll add this later
    show: false, // Don't show until ready
  })

  // Load the app
  if (isDev) {
    // Try different ports in case 3000 is occupied
    const devUrl = process.env.VITE_DEV_SERVER_URL || 'http://localhost:3000'
    mainWindow.loadURL(devUrl)
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// App event handlers
app.whenReady().then(() => {
  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  // Kill any running slsk process
  if (slskProcess) {
    slskProcess.kill()
  }

  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  // Clean up any running processes
  if (slskProcess) {
    slskProcess.kill()
  }
})

// IPC handlers
ipcMain.handle('select-directory', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory'],
  })
  return result.canceled ? null : result.filePaths[0]
})

ipcMain.handle('select-file', async (_, filters?: Electron.FileFilter[]) => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openFile'],
    filters: filters || [{ name: 'All Files', extensions: ['*'] }],
  })
  return result.canceled ? null : result.filePaths[0]
})

ipcMain.handle('save-file', async (_, defaultPath?: string, filters?: Electron.FileFilter[]) => {
  const result = await dialog.showSaveDialog(mainWindow!, {
    defaultPath,
    filters: filters || [{ name: 'All Files', extensions: ['*'] }],
  })
  return result.canceled ? null : result.filePath
})

ipcMain.handle('read-file', async (_, filePath: string) => {
  try {
    if (!existsSync(filePath)) {
      return null
    }
    return readFileSync(filePath, 'utf-8')
  } catch (error) {
    console.error('Error reading file:', error)
    return null
  }
})

ipcMain.handle('write-file', async (_, filePath: string, content: string) => {
  try {
    writeFileSync(filePath, content, 'utf-8')
    return true
  } catch (error) {
    console.error('Error writing file:', error)
    return false
  }
})

ipcMain.handle('file-exists', async (_, filePath: string) => {
  return existsSync(filePath)
})

// SLSK process management
ipcMain.handle('start-slsk-process', async (_, args: string[]) => {
  return new Promise((resolve, reject) => {
    try {
      // Kill existing process if running
      if (slskProcess) {
        slskProcess.kill()
      }

      // Find the slsk-batchdl executable
      // This assumes it's in the same directory or a known location
      const slskPath = join(__dirname, '../../../slsk-batchdl/slsk-batchdl') // Adjust path as needed

      slskProcess = spawn(slskPath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
      })

      let stdout = ''
      let stderr = ''

      slskProcess.stdout?.on('data', (data) => {
        stdout += data.toString()
        // Send real-time output to renderer
        mainWindow?.webContents.send('slsk-output', {
          type: 'stdout',
          data: data.toString(),
        })
      })

      slskProcess.stderr?.on('data', (data) => {
        stderr += data.toString()
        // Send real-time output to renderer
        mainWindow?.webContents.send('slsk-output', {
          type: 'stderr',
          data: data.toString(),
        })
      })

      slskProcess.on('close', (code) => {
        mainWindow?.webContents.send('slsk-process-exit', {
          code,
          stdout,
          stderr,
        })
        slskProcess = null
      })

      slskProcess.on('error', (error) => {
        reject(error)
        slskProcess = null
      })

      resolve({ pid: slskProcess.pid })
    } catch (error) {
      reject(error)
    }
  })
})

ipcMain.handle('stop-slsk-process', async () => {
  if (slskProcess) {
    slskProcess.kill()
    slskProcess = null
    return true
  }
  return false
})

ipcMain.handle('send-to-slsk-process', async (_, input: string) => {
  if (slskProcess && slskProcess.stdin) {
    slskProcess.stdin.write(input + '\n')
    return true
  }
  return false
})

// New CLI process management for multiple processes
ipcMain.handle('start-cli-process', async (_, args: string[]) => {
  return new Promise((resolve, reject) => {
    try {
      // TODO: Replace with actual path to slsk-batchdl executable
      const executablePath = isDev
        ? 'dotnet' // For development, use dotnet to run the project
        : join(process.resourcesPath, 'slsk-batchdl.exe') // For production

      const processArgs = isDev
        ? ['run', '--project', '../slsk-batchdl', '--', ...args] // Development args
        : args // Production args

      const childProcess = spawn(executablePath, processArgs, {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: isDev ? join(__dirname, '../../..') : undefined,
      })

      const pid = childProcess.pid!
      activeProcesses.set(pid, childProcess)

      // Set up event listeners
      childProcess.stdout?.on('data', (data) => {
        mainWindow?.webContents.send('cli-output', pid, data.toString())
      })

      childProcess.stderr?.on('data', (data) => {
        mainWindow?.webContents.send('cli-error', pid, data.toString())
      })

      childProcess.on('exit', (code) => {
        activeProcesses.delete(pid)
        mainWindow?.webContents.send('cli-exit', pid, code)
      })

      childProcess.on('error', (error) => {
        activeProcesses.delete(pid)
        mainWindow?.webContents.send('cli-error', pid, error.message)
      })

      resolve({ pid })
    } catch (error) {
      reject(error)
    }
  })
})

ipcMain.handle('stop-cli-process', async (_, pid: number) => {
  const process = activeProcesses.get(pid)
  if (process) {
    process.kill()
    activeProcesses.delete(pid)
    return true
  }
  return false
})

// Get app version
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

// Show message box
ipcMain.handle('show-message-box', async (_, options: Electron.MessageBoxOptions) => {
  const result = await dialog.showMessageBox(mainWindow!, options)
  return result
})
